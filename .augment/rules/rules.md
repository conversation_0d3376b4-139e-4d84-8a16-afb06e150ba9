---
type: "always_apply"
---

## Research & Documentation
1. **Always use latest documentation**: Before implementing anything, use the context7 MCP server to fetch the most current documentation for frameworks, libraries, and tools (Next.js, Supabase, React, etc.). Never rely on potentially outdated training data.

2. **Verify API changes**: Check for breaking changes, deprecated methods, and new best practices in the latest docs before suggesting code.

## Planning & Execution
3. **Think step-by-step**: Always break down complex tasks into logical steps and explain your reasoning before writing code.

4. **Analyze requirements first**: Understand the full scope of what needs to be built before starting implementation.

5. **Consider dependencies**: Check what packages, versions, and configurations are needed.

## Code Quality
6. **Follow current best practices**: Use the latest recommended patterns, folder structures, and conventions from official documentation.

7. **Write clean, maintainable code**: Prioritize readability, proper naming conventions, and modular design.

8. **Include proper error handling**: Always implement appropriate error boundaries, try-catch blocks, and user-friendly error messages.

## Testing & Validation
9. **Suggest testing approach**: Recommend appropriate testing strategies and provide test examples when relevant.

10. **Validate against requirements**: Ensure the implementation meets all specified requirements before considering it complete.

## Communication
11. **Explain your decisions**: When choosing between different approaches, explain why you selected a particular solution.

12. **Provide context**: Include relevant comments in code and explain any complex logic or architectural decisions.

13. **Suggest improvements**: When reviewing existing code, proactively suggest optimizations and better practices.

## File Management
14. **Organize code properly**: Follow framework-specific file organization patterns and create appropriate folder structures.

15. **Update related files**: When making changes, consider and update all related configuration files, types, and documentation.

## Performance & Security
16. **Consider performance**: Suggest optimizations for loading times, bundle size, and runtime performance.

17. **Implement security best practices**: Include proper authentication, authorization, input validation, and data sanitization.

18. **Environment awareness**: Use appropriate environment variables and configuration for different deployment stages.