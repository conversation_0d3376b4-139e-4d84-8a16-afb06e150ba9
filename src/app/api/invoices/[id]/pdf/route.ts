import { NextRequest, NextResponse } from 'next/server'
import { getInvoiceWithCompanyData } from '@/lib/api/invoice-pdf'
import { InvoicePDFGenerator } from '@/lib/pdf/invoice-pdf-generator'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: invoiceId } = await params

    if (!invoiceId) {
      return NextResponse.json(
        { error: 'Invoice ID is required' },
        { status: 400 }
      )
    }

    console.log('Generating PDF for invoice:', invoiceId)

    // Get invoice data with company information
    const data = await getInvoiceWithCompanyData(invoiceId)

    if (!data.invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    console.log('Invoice data retrieved successfully')

    // Generate PDF using Puppeteer
    console.log('Generating PDF with Puppeteer...')
    const pdfBuffer = await InvoicePDFGenerator.generatePDF(data)
    console.log('PDF generated successfully, size:', pdfBuffer.length, 'bytes')

    // Return PDF response with proper headers
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="invoice-${data.invoice.invoice_number}.pdf"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
    })
  } catch (error) {
    console.error('PDF generation error:', error)

    // Return more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'

    return NextResponse.json(
      {
        error: 'Failed to generate PDF',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
