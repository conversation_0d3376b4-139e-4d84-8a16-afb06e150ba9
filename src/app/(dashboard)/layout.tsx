import { redirect } from "next/navigation"
import { getCurrent<PERSON>ser, getUserProfile } from "@/lib/auth/actions"
import { AppSidebar } from "@/components/layout/app-sidebar"
import { DashboardHeader } from "@/components/layout/dashboard-header"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const user = await getCurrentUser()
  
  if (!user) {
    redirect("/login")
  }
  
  const profile = await getUserProfile()
  
  if (!profile) {
    redirect("/login")
  }

  return (
    <SidebarProvider>
      <AppSidebar user={profile} />
      <SidebarInset>
        <DashboardHeader />
        <div className="flex flex-1 flex-col">
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
