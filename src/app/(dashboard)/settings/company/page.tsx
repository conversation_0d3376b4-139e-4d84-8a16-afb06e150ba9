import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { PageHeader } from "@/components/layout/page-header"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Building2, 
  Users, 
  FileText, 
  BarChart3, 
  <PERSON>lette,
  Plus,
  Settings,
  Target,
  Award
} from "lucide-react"

export default function CompanyPage() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <PageHeader
        title="Company"
        description="Manage your company profile, team, and business operations"
      />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Company Profile */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Company Profile
            </CardTitle>
            <CardDescription>
              Detailed company information and business overview
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Business Overview</h4>
                <p className="text-sm text-muted-foreground">
                  HarunStudio is a digital agency specializing in web development, 
                  design, and digital marketing solutions for Indonesian businesses.
                </p>
              </div>
              
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Services</h4>
                <div className="flex flex-wrap gap-1">
                  <Badge variant="secondary">Web Development</Badge>
                  <Badge variant="secondary">WordPress</Badge>
                  <Badge variant="secondary">SEO</Badge>
                  <Badge variant="secondary">Hosting</Badge>
                </div>
              </div>

              <Button variant="outline" size="sm" className="w-full">
                <Settings className="h-4 w-4 mr-2" />
                Edit Profile
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Team Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Team Management
            </CardTitle>
            <CardDescription>
              Manage team members and organizational structure
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Team Members</span>
                  <Badge variant="outline">3 Active</Badge>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span>Willya Randika</span>
                    <Badge variant="secondary">Admin</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Team Member 2</span>
                    <Badge variant="secondary">Manager</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Team Member 3</span>
                    <Badge variant="secondary">Employee</Badge>
                  </div>
                </div>
              </div>

              <Button variant="outline" size="sm" className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Add Team Member
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Business Documents */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Business Documents
            </CardTitle>
            <CardDescription>
              Contract templates and legal documents
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Document Templates</h4>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span>Service Agreement</span>
                    <Badge variant="outline">Template</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Project Proposal</span>
                    <Badge variant="outline">Template</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>NDA Template</span>
                    <Badge variant="outline">Template</Badge>
                  </div>
                </div>
              </div>

              <Button variant="outline" size="sm" className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Add Template
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Company Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Company Analytics
            </CardTitle>
            <CardDescription>
              Business performance and growth metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-2xl font-bold">2</p>
                  <p className="text-xs text-muted-foreground">Active Projects</p>
                </div>
                <div className="space-y-1">
                  <p className="text-2xl font-bold">5</p>
                  <p className="text-xs text-muted-foreground">Total Clients</p>
                </div>
                <div className="space-y-1">
                  <p className="text-2xl font-bold">95%</p>
                  <p className="text-xs text-muted-foreground">Client Satisfaction</p>
                </div>
                <div className="space-y-1">
                  <p className="text-2xl font-bold">12</p>
                  <p className="text-xs text-muted-foreground">Months Active</p>
                </div>
              </div>

              <Button variant="outline" size="sm" className="w-full">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Detailed Reports
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Brand Guidelines */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Brand Guidelines
            </CardTitle>
            <CardDescription>
              Brand colors, fonts, and marketing materials
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Brand Colors</h4>
                <div className="flex gap-2">
                  <div className="w-8 h-8 rounded bg-blue-600 border"></div>
                  <div className="w-8 h-8 rounded bg-slate-600 border"></div>
                  <div className="w-8 h-8 rounded bg-sky-500 border"></div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="text-sm font-medium">Marketing Assets</h4>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span>Logo Variations</span>
                    <Badge variant="outline">3 Files</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Business Cards</span>
                    <Badge variant="outline">2 Files</Badge>
                  </div>
                </div>
              </div>

              <Button variant="outline" size="sm" className="w-full">
                <Palette className="h-4 w-4 mr-2" />
                Manage Brand Assets
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Company Goals */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Company Goals
            </CardTitle>
            <CardDescription>
              Business objectives and milestones
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium">2025 Objectives</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Award className="h-4 w-4 text-green-600" />
                    <span className="text-sm">Reach 50 clients</span>
                    <Badge variant="secondary">In Progress</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-blue-600" />
                    <span className="text-sm">Launch new services</span>
                    <Badge variant="outline">Planned</Badge>
                  </div>
                </div>
              </div>

              <Button variant="outline" size="sm" className="w-full">
                <Target className="h-4 w-4 mr-2" />
                Set New Goals
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
