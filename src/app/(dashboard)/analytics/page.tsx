import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/layout/page-header"
import { getAnalyticsData } from "@/lib/api/analytics"
import { AnalyticsCards } from "@/components/analytics/analytics-cards"
import { RevenueChart } from "@/components/analytics/revenue-chart"
import { RecentActivity } from "@/components/analytics/recent-activity"
import { ServiceTypeChart } from "@/components/analytics/service-type-chart"
import { Suspense } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Loader2 } from "lucide-react"

// Force dynamic rendering since we use cookies for authentication
export const dynamic = 'force-dynamic'

function LoadingCard() {
  return (
    <Card>
      <CardContent className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </CardContent>
    </Card>
  )
}

async function AnalyticsContent() {
  try {
    const data = await getAnalyticsData()

    return (
      <div className="space-y-6">
        {/* Key Metrics Cards */}
        <AnalyticsCards data={data} />

        {/* Charts Row */}
        <div className="grid gap-4 md:grid-cols-2">
          <RevenueChart data={data} />
          <ServiceTypeChart data={data} />
        </div>

        {/* Recent Activity */}
        <RecentActivity data={data} />
      </div>
    )
  } catch (error) {
    console.error('Failed to load analytics data:', error)
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <p className="text-muted-foreground">
            Failed to load analytics data. Please try again later.
          </p>
        </CardContent>
      </Card>
    )
  }
}

export default function AnalyticsPage() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <PageHeader
        title="Analytics"
        description="Business insights and performance metrics"
      />

      <Suspense fallback={<LoadingCard />}>
        <AnalyticsContent />
      </Suspense>
    </div>
  )
}
