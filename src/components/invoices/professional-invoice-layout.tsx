"use client"

import React from "react"
import Image from "next/image"
import { formatInvoiceCurrency } from "@/lib/utils/currency"
import { InvoiceWithRelations, InvoiceItem } from "@/lib/types"
import { CompanySettings, BankAccount, InvoiceTemplate } from "@/lib/types/company"
import { format } from "date-fns"
import { id as idLocale } from "date-fns/locale"

// Type guard for invoice items
function isInvoiceItemArray(items: unknown): items is InvoiceItem[] {
  return Array.isArray(items) && items.every(item =>
    typeof item === 'object' &&
    item !== null &&
    'description' in item &&
    'quantity' in item &&
    'rate' in item &&
    'amount' in item
  )
}

interface ProfessionalInvoiceLayoutProps {
  invoice: InvoiceWithRelations
  company: CompanySettings
  template?: InvoiceTemplate | null
  bankAccounts: BankAccount[]
  className?: string
  isPrintMode?: boolean
}

export function ProfessionalInvoiceLayout({
  invoice,
  company,
  template,
  bankAccounts,
  className = "",
  isPrintMode = false
}: ProfessionalInvoiceLayoutProps) {
  const primaryBankAccount = bankAccounts.find(account => 
    account.is_primary && account.currency === invoice.currency
  ) || bankAccounts.find(account => account.currency === invoice.currency) || bankAccounts[0]

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    if (template?.indonesian_settings?.date_format === 'dd/mm/yyyy') {
      return format(date, 'dd/MM/yyyy')
    } else if (template?.indonesian_settings?.date_format === 'dd-mm-yyyy') {
      return format(date, 'dd-MM-yyyy')
    }
    return format(date, 'dd MMM yyyy', { locale: idLocale })
  }

  const showIndonesian = template?.indonesian_settings?.language === 'id' || template?.indonesian_settings?.language === 'both'
  const showEnglish = template?.indonesian_settings?.language === 'en' || template?.indonesian_settings?.language === 'both'

  return (
    <div className={`bg-white ${isPrintMode ? 'print-layout' : ''} ${className}`}>
      {/* Enhanced Modern Header with Company Information */}
      <div className="invoice-header mb-16">
        <div className="flex items-start justify-between">
          {/* Company Logo and Information */}
          <div className="flex flex-col">
            {template?.layout?.show_logo && company.logo_url && (
              <div className="company-logo mb-6">
                <Image
                  src={company.logo_url}
                  alt={company.name}
                  width={240}
                  height={96}
                  className="h-20 w-auto object-contain"
                />
              </div>
            )}

            {/* Company Information */}
            <div className="space-y-2">
              <p className="font-semibold text-gray-900 text-lg">{company.name}</p>
              <div className="text-sm text-gray-600 space-y-1">
                {company.address && (
                  <div className="space-y-1">
                    <p>{company.address.street}</p>
                    <p>{company.address.city}, {company.address.state} {company.address.postal_code}</p>
                    <p>{company.address.country}</p>
                  </div>
                )}
                <div className="pt-2 space-y-1">
                  <p>{company.email}</p>
                  <p>{company.phone}</p>
                  {company.website && <p>{company.website}</p>}
                  {template?.content?.show_tax_id && company.tax_id && (
                    <p className="text-xs text-gray-500">NPWP: {company.tax_id}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Invoice Title, Number and Details - Modern Style */}
          <div className="text-right">
            <div className="text-sm font-medium text-gray-500 mb-2 uppercase tracking-wide">
              {showIndonesian && showEnglish ? 'Invoice / Faktur' :
               showIndonesian ? 'Faktur' : 'Invoice'}
            </div>
            <div className="text-3xl font-bold text-gray-900 mb-3">
              #{invoice.invoice_number}
            </div>
            {invoice.milestone_type && invoice.milestone_type !== 'standard' && (
              <div className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-50 text-blue-700 border border-blue-200 mb-6">
                {invoice.milestone_type === 'dp' && (showIndonesian ? 'Uang Muka' : 'Down Payment')}
                {invoice.milestone_type === 'progress' && (showIndonesian ? 'Pembayaran Bertahap' : 'Progress Payment')}
                {invoice.milestone_type === 'final' && (showIndonesian ? 'Pembayaran Akhir' : 'Final Payment')}
                {invoice.milestone_percentage && ` (${invoice.milestone_percentage}%)`}
              </div>
            )}

            {/* Invoice Details - Moved to Header */}
            <div className="mt-6">
              <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-4">
                {showIndonesian && showEnglish ? 'Invoice Details / Detail Faktur' :
                 showIndonesian ? 'Detail Faktur' : 'Invoice Details'}
              </div>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">
                    {showIndonesian ? 'Tanggal Faktur' : 'Invoice Date'}
                  </span>
                  <span className="font-medium text-gray-900">{formatDate(invoice.created_at)}</span>
                </div>
                {invoice.due_date && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">
                      {showIndonesian ? 'Jatuh Tempo' : 'Due Date'}
                    </span>
                    <span className="font-medium text-gray-900">{formatDate(invoice.due_date)}</span>
                  </div>
                )}
                {invoice.project && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">
                      {showIndonesian ? 'Proyek' : 'Project'}
                    </span>
                    <span className="font-medium text-gray-900">{invoice.project.name}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="h-px bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 my-12"></div>

      {/* Enhanced Bill To Section - Wider Layout */}
      <div className="mb-16">
        <div className="bg-gray-50 rounded-lg p-8 max-w-4xl">
          <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-6">
            {showIndonesian && showEnglish ? 'Bill To / Tagihan Kepada' :
             showIndonesian ? 'Tagihan Kepada' : 'Bill To'}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-3">
              <p className="font-medium text-gray-900 text-lg">{invoice.client?.name}</p>
              {invoice.client?.company && (
                <p className="text-gray-600 font-medium">{invoice.client.company}</p>
              )}
              {invoice.client?.address && (
                <div className="text-gray-500 text-sm space-y-1 mt-3">
                  {typeof invoice.client.address === 'object' && (
                    <>
                      {invoice.client.address.street && <p>{invoice.client.address.street}</p>}
                      {(invoice.client.address.city || invoice.client.address.state) && (
                        <p>
                          {[invoice.client.address.city, invoice.client.address.state]
                            .filter(Boolean)
                            .join(', ')}
                        </p>
                      )}
                      {invoice.client.address.postal_code && <p>{invoice.client.address.postal_code}</p>}
                    </>
                  )}
                </div>
              )}
            </div>
            <div className="space-y-2">
              {invoice.client?.email && (
                <p className="text-gray-600 text-sm">{invoice.client.email}</p>
              )}
              {invoice.client?.phone && (
                <p className="text-gray-600 text-sm">{invoice.client.phone}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Invoice Items Table */}
      <div className="mb-12">
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          {/* Table Header */}
          <div className="bg-gray-50 px-8 py-4 border-b border-gray-200">
            <div className="grid grid-cols-12 gap-4 text-base font-semibold text-gray-900">
              <div className="col-span-6">
                {showIndonesian && showEnglish ? 'Description / Deskripsi' :
                 showIndonesian ? 'Deskripsi' : 'Description'}
              </div>
              <div className="col-span-2 text-center">
                {showIndonesian ? 'Jumlah' : 'Qty'}
              </div>
              <div className="col-span-2 text-center">
                {showIndonesian ? 'Harga' : 'Rate'}
              </div>
              <div className="col-span-2 text-right">
                {showIndonesian ? 'Total' : 'Amount'}
              </div>
            </div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-gray-200">
            {invoice.items && isInvoiceItemArray(invoice.items) && invoice.items.map((item, index) => (
              <div key={index} className="px-8 py-6">
                <div className="grid grid-cols-12 gap-4 text-base">
                  <div className="col-span-6 text-gray-900 leading-relaxed">{item.description}</div>
                  <div className="col-span-2 text-center text-gray-700">{item.quantity}</div>
                  <div className="col-span-2 text-center text-gray-700">
                    {formatInvoiceCurrency(item.rate, invoice.currency as 'IDR' | 'USD')}
                  </div>
                  <div className="col-span-2 text-right font-medium text-gray-900">
                    {formatInvoiceCurrency(item.amount, invoice.currency as 'IDR' | 'USD')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Enhanced Totals Section with Flexible Line Items */}
      <div className="flex justify-end mb-12">
        <div className="w-full max-w-md">
          <div className="space-y-3 text-base">
            <div className="flex justify-between py-3">
              <span className="text-gray-600">
                {showIndonesian ? 'Subtotal:' : 'Subtotal:'}
              </span>
              <span className="font-medium">
                {formatInvoiceCurrency(invoice.amount, invoice.currency as 'IDR' | 'USD')}
              </span>
            </div>

            {/* Discount Line Item */}
            {invoice.discount_amount && invoice.discount_amount > 0 && (
              <div className="flex justify-between py-3">
                <span className="text-gray-600">
                  {showIndonesian ? 'Diskon:' : 'Discount:'}
                  {invoice.discount_type === 'percentage' && ` (${invoice.discount_amount}%)`}
                </span>
                <span className="font-medium text-red-600">
                  -{formatInvoiceCurrency(
                    invoice.discount_type === 'percentage'
                      ? (invoice.amount * invoice.discount_amount / 100)
                      : invoice.discount_amount,
                    invoice.currency as 'IDR' | 'USD'
                  )}
                </span>
              </div>
            )}

            {/* Tax Line Item */}
            {invoice.tax_amount > 0 && (
              <div className="flex justify-between py-3">
                <span className="text-gray-600">
                  {showIndonesian ? 'Pajak:' : 'Tax:'}
                </span>
                <span className="font-medium">
                  {formatInvoiceCurrency(invoice.tax_amount, invoice.currency as 'IDR' | 'USD')}
                </span>
              </div>
            )}

            <div className="h-px bg-gray-200 my-4"></div>

            <div className="flex justify-between py-4 text-lg font-bold">
              <span className="text-gray-900">
                {showIndonesian ? 'Total:' : 'Total:'}
              </span>
              <span className="text-gray-900">
                {formatInvoiceCurrency(invoice.total_amount, invoice.currency as 'IDR' | 'USD')}
              </span>
            </div>
          </div>
        </div>
      </div>



      {/* Invoice Notes (only if specified on the invoice) */}
      {invoice.notes && (
        <div className="mb-12">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {showIndonesian ? 'Catatan' : 'Notes'}
          </h3>
          <p className="text-sm text-gray-600 leading-relaxed">{invoice.notes}</p>
        </div>
      )}

      {/* Enhanced Payment Information Footer */}
      <div className="mt-20 pt-10 border-t border-gray-200">
        {/* Essential Payment Information Only */}
        {template?.content?.show_bank_details && primaryBankAccount && (
          <div className="bg-gray-50 rounded-lg p-8 mb-8">
            <div className="text-center">
              <div className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-6">
                {showIndonesian ? 'Informasi Pembayaran' : 'Payment Information'}
              </div>
              <div className="space-y-4">
                <p className="font-semibold text-gray-900 text-xl">{primaryBankAccount.bank_name}</p>
                <div className="flex justify-center items-center space-x-12 text-sm">
                  <div className="text-center">
                    <p className="text-gray-500 text-xs uppercase tracking-wide mb-2">
                      {showIndonesian ? 'Nama Rekening' : 'Account Name'}
                    </p>
                    <p className="font-medium text-gray-900 text-base">{primaryBankAccount.account_name}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-gray-500 text-xs uppercase tracking-wide mb-2">
                      {showIndonesian ? 'Nomor Rekening' : 'Account Number'}
                    </p>
                    <p className="font-mono font-bold text-gray-900 text-xl">{primaryBankAccount.account_number}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer Text */}
        {company.invoice_settings?.invoice_footer_text && (
          <div className="text-center mt-8 pt-6 border-t border-gray-100">
            <p className="text-sm text-gray-500 leading-relaxed">
              {company.invoice_settings.invoice_footer_text}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
