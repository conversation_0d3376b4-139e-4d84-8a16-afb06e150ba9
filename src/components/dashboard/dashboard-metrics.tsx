'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, TrendingDown, DollarSign, FileText, Users, Briefcase } from "lucide-react"
import { DashboardData } from "@/lib/api/dashboard"

interface DashboardMetricsProps {
  data: DashboardData
}

export function DashboardMetrics({ data }: DashboardMetricsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  const metrics = [
    {
      title: "Total Clients",
      value: data.metrics.totalClients.toString(),
      icon: Users,
      description: `${data.metrics.activeClients} active`,
      color: "text-blue-600"
    },
    {
      title: "Active Projects",
      value: data.metrics.activeProjects.toString(),
      icon: Briefcase,
      description: `${data.metrics.totalProjects} total`,
      color: "text-green-600"
    },
    {
      title: "Pending Invoices",
      value: data.metrics.pendingInvoices.toString(),
      icon: FileText,
      description: `${data.metrics.totalInvoices} total`,
      color: "text-yellow-600"
    },
    {
      title: "Total Revenue",
      value: formatCurrency(data.metrics.totalRevenue),
      icon: DollarSign,
      description: "All time",
      color: "text-green-600"
    },
    {
      title: "Monthly Revenue",
      value: formatCurrency(data.metrics.monthlyRevenue),
      icon: TrendingUp,
      description: "This month",
      color: "text-blue-600",
      trend: data.metrics.revenueGrowth
    }
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
      {metrics.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {metric.title}
            </CardTitle>
            <metric.icon className={`h-4 w-4 ${metric.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metric.value}</div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground">
                {metric.description}
              </p>
              {metric.trend !== undefined && metric.trend !== 0 && (
                <Badge variant={metric.trend > 0 ? "default" : "destructive"} className="text-xs">
                  {metric.trend > 0 ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1" />
                  )}
                  {formatPercentage(metric.trend)}
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
