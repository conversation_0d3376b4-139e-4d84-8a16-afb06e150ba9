'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { FileText, Users, FolderOpen, BarChart3 } from "lucide-react"
import { DashboardData } from "@/lib/api/dashboard"
import Link from "next/link"

interface QuickActionsProps {
  data: DashboardData
}

const iconMap = {
  FileText,
  Users,
  FolderOpen,
  BarChart3
}

export function QuickActions({ data }: QuickActionsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
        <CardDescription>
          Common tasks to help you manage your business efficiently
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
          {data.quickActions.map((action) => {
            const IconComponent = iconMap[action.icon as keyof typeof iconMap]
            
            return (
              <Link key={action.title} href={action.href}>
                <Button 
                  variant="outline" 
                  className="h-auto p-4 flex flex-col items-center space-y-2 w-full hover:bg-muted/50 transition-colors"
                >
                  <IconComponent className="h-6 w-6 text-primary" />
                  <div className="text-center">
                    <div className="font-medium text-sm">{action.title}</div>
                    <div className="text-xs text-muted-foreground">{action.description}</div>
                  </div>
                </Button>
              </Link>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
