'use client'

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, TrendingDown, DollarSign, FileText, Users, Briefcase } from "lucide-react"
import { AnalyticsData } from "@/lib/api/analytics"

interface AnalyticsCardsProps {
  data: AnalyticsData
}

export function AnalyticsCards({ data }: AnalyticsCardsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Revenue Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(data.revenue.total)}</div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
            <span>This month: {formatCurrency(data.revenue.thisMonth)}</span>
            {data.revenue.growth !== 0 && (
              <Badge variant={data.revenue.growth > 0 ? "default" : "destructive"} className="text-xs">
                {data.revenue.growth > 0 ? (
                  <TrendingUp className="h-3 w-3 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1" />
                )}
                {formatPercentage(data.revenue.growth)}
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Invoices Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Invoices</CardTitle>
          <FileText className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{data.invoices.total}</div>
          <div className="space-y-1 text-xs text-muted-foreground">
            <div className="flex justify-between">
              <span>Paid:</span>
              <span className="text-green-600">{data.invoices.paid}</span>
            </div>
            <div className="flex justify-between">
              <span>Pending:</span>
              <span className="text-yellow-600">{data.invoices.pending}</span>
            </div>
            <div className="flex justify-between">
              <span>Overdue:</span>
              <span className="text-red-600">{data.invoices.overdue}</span>
            </div>
            <div className="pt-1 border-t">
              <Badge variant="outline" className="text-xs">
                {data.invoices.paidPercentage.toFixed(1)}% paid
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Clients Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Clients</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{data.clients.total}</div>
          <div className="space-y-1 text-xs text-muted-foreground">
            <div className="flex justify-between">
              <span>Active:</span>
              <span className="text-green-600">{data.clients.active}</span>
            </div>
            <div className="flex justify-between">
              <span>Leads:</span>
              <span className="text-blue-600">{data.clients.leads}</span>
            </div>
            <div className="flex justify-between">
              <span>Inactive:</span>
              <span className="text-gray-600">{data.clients.inactive}</span>
            </div>
            {data.clients.newThisMonth > 0 && (
              <div className="pt-1 border-t">
                <Badge variant="default" className="text-xs">
                  +{data.clients.newThisMonth} this month
                </Badge>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Projects Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Projects</CardTitle>
          <Briefcase className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{data.projects.total}</div>
          <div className="space-y-1 text-xs text-muted-foreground">
            <div className="flex justify-between">
              <span>Active:</span>
              <span className="text-blue-600">{data.projects.active}</span>
            </div>
            <div className="flex justify-between">
              <span>Completed:</span>
              <span className="text-green-600">{data.projects.completed}</span>
            </div>
            <div className="flex justify-between">
              <span>In Progress:</span>
              <span className="text-yellow-600">{data.projects.in_progress}</span>
            </div>
            <div className="pt-1 border-t">
              <Badge variant="outline" className="text-xs">
                {data.projects.completionRate.toFixed(1)}% completion rate
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
