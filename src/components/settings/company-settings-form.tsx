"use client"

import React, { useState, useEffect, useCallback } from "react"
import Image from "next/image"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Loader2, Upload, Building } from "lucide-react"
import { getCompanySettings, updateCompanySettings, uploadCompanyLogo } from "@/lib/api/company-settings"

const companySettingsSchema = z.object({
  name: z.string().min(1, "Company name is required"),
  legal_name: z.string().optional(),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(1, "Phone number is required"),
  website: z.string().url().optional().or(z.literal("")),
  address: z.object({
    street: z.string().min(1, "Street address is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    postal_code: z.string().min(1, "Postal code is required"),
    country: z.string().min(1, "Country is required"),
  }),
  tax_id: z.string().optional(),
  business_license: z.string().optional(),
  company_registration: z.string().optional(),
  invoice_settings: z.object({
    default_payment_terms: z.number().min(1).max(365),
    payment_terms_text: z.string().optional(),
    late_fee_percentage: z.number().min(0).max(100).optional(),
    invoice_footer_text: z.string().optional(),
    invoice_notes: z.string().optional(),
  }),
})

type CompanySettingsFormData = z.infer<typeof companySettingsSchema>

export function CompanySettingsForm() {
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [logoUploading, setLogoUploading] = useState(false)
  const [currentLogo, setCurrentLogo] = useState<string | null>(null)

  const form = useForm<CompanySettingsFormData>({
    resolver: zodResolver(companySettingsSchema),
    defaultValues: {
      name: "",
      legal_name: "",
      email: "",
      phone: "",
      website: "",
      address: {
        street: "",
        city: "",
        state: "",
        postal_code: "",
        country: "Indonesia",
      },
      tax_id: "",
      business_license: "",
      company_registration: "",
      invoice_settings: {
        default_payment_terms: 30,
        payment_terms_text: "",
        late_fee_percentage: 0,
        invoice_footer_text: "Thank you for your business!",
        invoice_notes: "",
      },
    },
  })

  const loadCompanySettings = useCallback(async () => {
    setLoading(true)
    try {
      const settings = await getCompanySettings()
      
      form.reset({
        name: settings.name || "",
        legal_name: settings.legal_name || "",
        email: settings.email || "",
        phone: settings.phone || "",
        website: settings.website || "",
        address: {
          street: settings.address?.street || "",
          city: settings.address?.city || "",
          state: settings.address?.state || "",
          postal_code: settings.address?.postal_code || "",
          country: settings.address?.country || "Indonesia",
        },
        tax_id: settings.tax_id || "",
        business_license: settings.business_license || "",
        company_registration: settings.company_registration || "",
        invoice_settings: {
          default_payment_terms: settings.invoice_settings?.default_payment_terms || 30,
          payment_terms_text: settings.invoice_settings?.payment_terms_text || "",
          late_fee_percentage: settings.invoice_settings?.late_fee_percentage || 0,
          invoice_footer_text: settings.invoice_settings?.invoice_footer_text || "",
          invoice_notes: settings.invoice_settings?.invoice_notes || "",
        },
      })
      
      setCurrentLogo(settings.logo_url || null)
    } catch (error) {
      console.error("Failed to load company settings:", error)
    } finally {
      setLoading(false)
    }
  }, [form])

  useEffect(() => {
    loadCompanySettings()
  }, [loadCompanySettings])

  const onSubmit = async (data: CompanySettingsFormData) => {
    setSaving(true)
    try {
      await updateCompanySettings({
        ...data,
        logo_url: currentLogo || undefined,
      })
      
      // Show success message (you might want to use a toast notification)
      alert("Company settings updated successfully!")
    } catch (error) {
      console.error("Failed to update company settings:", error)
      alert("Failed to update company settings. Please try again.")
    } finally {
      setSaving(false)
    }
  }

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file')
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB')
      return
    }

    setLogoUploading(true)
    try {
      const logoUrl = await uploadCompanyLogo(file)
      setCurrentLogo(logoUrl)
    } catch (error) {
      console.error("Failed to upload logo:", error)
      alert("Failed to upload logo. Please try again.")
    } finally {
      setLogoUploading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Loading company settings...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Company Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Company Information
              </CardTitle>
              <CardDescription>
                Basic information about your company that will appear on invoices
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Logo Upload */}
              <div>
                <FormLabel>Company Logo</FormLabel>
                <div className="mt-2 flex items-center gap-4">
                  {currentLogo && (
                    <div className="h-16 w-16 border border-gray-200 rounded-lg overflow-hidden">
                      <Image
                        src={currentLogo}
                        alt="Company Logo"
                        width={64}
                        height={64}
                        className="h-full w-full object-contain"
                      />
                    </div>
                  )}
                  <div>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      disabled={logoUploading}
                      className="hidden"
                      id="logo-upload"
                    />
                    <label htmlFor="logo-upload">
                      <Button
                        type="button"
                        variant="outline"
                        disabled={logoUploading}
                        className="cursor-pointer"
                        asChild
                      >
                        <span>
                          {logoUploading ? (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          ) : (
                            <Upload className="mr-2 h-4 w-4" />
                          )}
                          {currentLogo ? 'Change Logo' : 'Upload Logo'}
                        </span>
                      </Button>
                    </label>
                    <p className="text-xs text-muted-foreground mt-1">
                      PNG, JPG up to 5MB. Recommended: 200x80px
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="HarunStudio" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="legal_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Legal Name</FormLabel>
                      <FormControl>
                        <Input placeholder="PT HarunStudio Indonesia" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email *</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone *</FormLabel>
                      <FormControl>
                        <Input placeholder="+62 21 1234 5678" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <Input placeholder="https://harunstudio.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Address Information */}
          <Card>
            <CardHeader>
              <CardTitle>Address Information</CardTitle>
              <CardDescription>
                Company address that will appear on invoices
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="address.street"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Street Address *</FormLabel>
                    <FormControl>
                      <Input placeholder="Jl. Sudirman No. 123" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="address.city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City *</FormLabel>
                      <FormControl>
                        <Input placeholder="Jakarta" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address.state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State/Province *</FormLabel>
                      <FormControl>
                        <Input placeholder="DKI Jakarta" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="address.postal_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Postal Code *</FormLabel>
                      <FormControl>
                        <Input placeholder="12345" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address.country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Country *</FormLabel>
                      <FormControl>
                        <Input placeholder="Indonesia" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Indonesian Business Details */}
          <Card>
            <CardHeader>
              <CardTitle>Indonesian Business Details</CardTitle>
              <CardDescription>
                Business registration and tax information for Indonesian compliance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="tax_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>NPWP (Tax ID)</FormLabel>
                      <FormControl>
                        <Input placeholder="12.345.678.9-012.345" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="business_license"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>NIB (Business License)</FormLabel>
                      <FormControl>
                        <Input placeholder="1234567890123" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="company_registration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Registration (Akta Pendirian)</FormLabel>
                    <FormControl>
                      <Input placeholder="No. 123 dated 01 January 2020" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>



          {/* Save Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={saving}>
              {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Save Settings
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
