import { validateProjectBudget } from '@/lib/api/invoices-client'

export interface BudgetValidationResult {
  isValid: boolean
  totalInvoiced: number
  remainingBudget: number
  projectBudget: number
  wouldExceedBy?: number
  utilizationPercentage: number
}

export async function validateInvoiceAgainstBudget(
  projectId: string,
  invoiceAmount: number,
  excludeInvoiceId?: string
): Promise<BudgetValidationResult> {
  try {
    // Note: excludeInvoiceId parameter is reserved for future use when excluding specific invoices from budget calculation
    void excludeInvoiceId
    const result = await validateProjectBudget(projectId, invoiceAmount)
    
    const utilizationPercentage = result.projectBudget > 0 
      ? Math.round(((result.totalInvoiced + invoiceAmount) / result.projectBudget) * 100)
      : 0

    return {
      isValid: result.isValid,
      totalInvoiced: result.totalInvoiced,
      remainingBudget: result.remainingBudget,
      projectBudget: result.projectBudget,
      wouldExceedBy: result.wouldExceedBy,
      utilizationPercentage
    }
  } catch (error) {
    console.error('Budget validation error:', error)
    // Return a safe default that allows the operation
    return {
      isValid: true,
      totalInvoiced: 0,
      remainingBudget: 0,
      projectBudget: 0,
      utilizationPercentage: 0
    }
  }
}

export function getBudgetWarningLevel(utilizationPercentage: number): 'safe' | 'warning' | 'danger' {
  if (utilizationPercentage <= 80) return 'safe'
  if (utilizationPercentage <= 95) return 'warning'
  return 'danger'
}

export function getBudgetWarningMessage(
  utilizationPercentage: number, 
  remainingBudget: number,
  currency: string = 'IDR'
): string {
  const level = getBudgetWarningLevel(utilizationPercentage)
  
  switch (level) {
    case 'warning':
      return `Budget utilization is at ${utilizationPercentage}%. Remaining budget: ${formatCurrency(remainingBudget, currency)}`
    case 'danger':
      return `Budget utilization is at ${utilizationPercentage}%. This invoice may exceed the project budget.`
    default:
      return `Budget utilization: ${utilizationPercentage}%. Remaining budget: ${formatCurrency(remainingBudget, currency)}`
  }
}

function formatCurrency(amount: number, currency: string): string {
  if (currency === 'IDR') {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount)
}

export interface MilestoneValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  totalPercentage: number
  totalAmount: number
}

export function validateMilestonePercentages(
  milestones: Array<{ percentage: number; type: string }>,
  projectBudget: number
): MilestoneValidationResult {
  const errors: string[] = []
  const warnings: string[] = []
  
  // Calculate total percentage
  const totalPercentage = milestones.reduce((sum, milestone) => sum + milestone.percentage, 0)
  const totalAmount = Math.round(projectBudget * totalPercentage / 100)
  
  // Validate total percentage
  if (totalPercentage !== 100) {
    errors.push(`Total milestone percentages must equal 100%. Current total: ${totalPercentage}%`)
  }
  
  // Check for duplicate milestone types (except progress)
  const typeCount = milestones.reduce((acc, milestone) => {
    if (milestone.type !== 'progress') {
      acc[milestone.type] = (acc[milestone.type] || 0) + 1
    }
    return acc
  }, {} as Record<string, number>)
  
  Object.entries(typeCount).forEach(([type, count]) => {
    if (count > 1) {
      errors.push(`Only one ${type} milestone is allowed`)
    }
  })
  
  // Validate individual percentages
  milestones.forEach((milestone, index) => {
    if (milestone.percentage <= 0) {
      errors.push(`Milestone ${index + 1}: Percentage must be greater than 0`)
    }
    if (milestone.percentage > 100) {
      errors.push(`Milestone ${index + 1}: Percentage cannot exceed 100%`)
    }
  })
  
  // Warnings for common patterns
  const dpMilestone = milestones.find(m => m.type === 'dp')
  if (dpMilestone && dpMilestone.percentage < 20) {
    warnings.push('Down payment is less than 20% - consider increasing for better cash flow')
  }
  
  const finalMilestone = milestones.find(m => m.type === 'final')
  if (finalMilestone && finalMilestone.percentage > 50) {
    warnings.push('Final payment is more than 50% - consider breaking into progress payments')
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    totalPercentage,
    totalAmount
  }
}
