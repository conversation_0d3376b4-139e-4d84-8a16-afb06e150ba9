import { createClient } from '@/lib/supabase/client'
import { createClient as createServerClient } from '@/lib/supabase/server'
import { CreateProjectData, UpdateProjectData } from '@/lib/types'

export async function getProjects() {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(id, name, company),
      created_by_user:users_profiles!created_by(id, full_name)
    `)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to fetch projects: ${error.message}`)
  }
  
  return data
}

export async function getProject(id: string) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(*),
      created_by_user:users_profiles!created_by(id, full_name),
      invoices(*)
    `)
    .eq('id', id)
    .single()
  
  if (error) {
    throw new Error(`Failed to fetch project: ${error.message}`)
  }
  
  return data
}

export async function createProject(projectData: CreateProjectData) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('projects')
    .insert(projectData)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to create project: ${error.message}`)
  }
  
  return data
}

export async function updateProject(id: string, projectData: UpdateProjectData) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('projects')
    .update(projectData)
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to update project: ${error.message}`)
  }
  
  return data
}

export async function deleteProject(id: string) {
  const supabase = createClient()
  
  const { error } = await supabase
    .from('projects')
    .delete()
    .eq('id', id)
  
  if (error) {
    throw new Error(`Failed to delete project: ${error.message}`)
  }
}

// Server-side functions
export async function getProjectsServer() {
  const supabase = await createServerClient()
  
  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(id, name, company),
      created_by_user:users_profiles!created_by(id, full_name)
    `)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to fetch projects: ${error.message}`)
  }
  
  return data
}
