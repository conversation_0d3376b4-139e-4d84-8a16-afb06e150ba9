import { createClient } from '@/lib/supabase/server'

// Define types for recent activity data based on what the queries actually return
type RecentInvoice = {
  id: string
  invoice_number: string
  status: string
  total_amount: number
  created_at: string
  client: {
    name: string
    company: string | null
  }[]
}

type RecentProject = {
  id: string
  name: string
  status: string
  created_at: string
  client: {
    name: string
    company: string | null
  }[]
}

type RecentClient = {
  id: string
  name: string
  company: string | null
  status: string
  created_at: string
}

export interface DashboardData {
  metrics: {
    totalClients: number
    activeClients: number
    totalProjects: number
    activeProjects: number
    totalInvoices: number
    pendingInvoices: number
    totalRevenue: number
    monthlyRevenue: number
    revenueGrowth: number
  }
  recentActivity: {
    recentInvoices: RecentInvoice[]
    recentProjects: RecentProject[]
    recentClients: RecentClient[]
  }
  projectStatusDistribution: Array<{
    status: string
    count: number
    percentage: number
  }>
  quickActions: Array<{
    title: string
    description: string
    href: string
    icon: string
  }>
}

export interface ChartData {
  name: string
  value: number
  date?: string
}

export async function getRevenueChartData(): Promise<ChartData[]> {
  const supabase = await createClient()

  // Get current date info for proper 12-month rolling window
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth()

  const { data, error } = await supabase
    .from('invoices')
    .select('total_amount, invoice_date')
    .eq('status', 'paid')
    .not('invoice_date', 'is', null)
    .gte('invoice_date', new Date(currentYear - 1, currentMonth, 1).toISOString().split('T')[0])
    .order('invoice_date', { ascending: true })

  if (error) {
    throw new Error(`Failed to fetch revenue chart data: ${error.message}`)
  }

  // Generate 12-month rolling window with proper month/year labels
  const monthlyRevenue = []
  for (let i = 11; i >= 0; i--) {
    const date = new Date(currentYear, currentMonth - i, 1)
    const monthStart = new Date(date.getFullYear(), date.getMonth(), 1)
    const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0)

    const monthData = data.filter(inv => {
      const invDate = new Date(inv.invoice_date!)
      return invDate >= monthStart && invDate <= monthEnd
    })

    const monthRevenue = monthData.reduce((sum, inv) => sum + Number(inv.total_amount), 0)

    monthlyRevenue.push({
      name: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      value: monthRevenue,
      date: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
    })
  }

  return monthlyRevenue
}

export async function getProjectStatusData(): Promise<ChartData[]> {
  const supabase = await createClient()

  const { data, error } = await supabase
    .from('projects')
    .select('status')

  if (error) {
    throw new Error(`Failed to fetch project status data: ${error.message}`)
  }

  const statusCounts: { [key: string]: number } = {}

  data.forEach((project) => {
    statusCounts[project.status] = (statusCounts[project.status] || 0) + 1
  })

  return Object.entries(statusCounts).map(([name, value]) => ({
    name: name.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    value
  }))
}

export async function getDashboardData(): Promise<DashboardData> {
  const supabase = await createClient()

  // Get current date info for monthly calculations
  const now = new Date()
  const currentMonth = now.getMonth()
  const currentYear = now.getFullYear()
  const firstDayThisMonth = new Date(currentYear, currentMonth, 1).toISOString()
  const firstDayLastMonth = new Date(currentYear, currentMonth - 1, 1).toISOString()
  const firstDayNextMonth = new Date(currentYear, currentMonth + 1, 1).toISOString()

  // Fetch all data in parallel
  const [
    clientsResult,
    projectsResult,
    invoicesResult,
    recentInvoicesResult,
    recentProjectsResult,
    recentClientsResult
  ] = await Promise.all([
    // All clients
    supabase
      .from('clients')
      .select('id, status, created_at')
      .order('created_at', { ascending: false }),

    // All projects
    supabase
      .from('projects')
      .select('id, status, created_at')
      .order('created_at', { ascending: false }),

    // All invoices
    supabase
      .from('invoices')
      .select('id, status, total_amount, invoice_date, created_at')
      .order('created_at', { ascending: false }),

    // Recent invoices (last 3)
    supabase
      .from('invoices')
      .select(`
        id, invoice_number, status, total_amount, created_at,
        client:clients!inner(name, company)
      `)
      .order('created_at', { ascending: false })
      .limit(3),

    // Recent projects (last 3)
    supabase
      .from('projects')
      .select(`
        id, name, status, created_at,
        client:clients!inner(name, company)
      `)
      .order('created_at', { ascending: false })
      .limit(3),

    // Recent clients (last 3)
    supabase
      .from('clients')
      .select('id, name, company, status, created_at')
      .order('created_at', { ascending: false })
      .limit(3)
  ])

  // Handle errors
  if (clientsResult.error) throw new Error(`Failed to fetch clients: ${clientsResult.error.message}`)
  if (projectsResult.error) throw new Error(`Failed to fetch projects: ${projectsResult.error.message}`)
  if (invoicesResult.error) throw new Error(`Failed to fetch invoices: ${invoicesResult.error.message}`)

  const clients = clientsResult.data || []
  const projects = projectsResult.data || []
  const invoices = invoicesResult.data || []
  const recentInvoices = recentInvoicesResult.data || []
  const recentProjects = recentProjectsResult.data || []
  const recentClients = recentClientsResult.data || []

  // Calculate metrics
  const totalClients = clients.length
  const activeClients = clients.filter(client => client.status === 'active').length
  const totalProjects = projects.length
  const activeProjects = projects.filter(project => project.status === 'in_progress').length
  const totalInvoices = invoices.length
  const pendingInvoices = invoices.filter(invoice => ['draft', 'sent'].includes(invoice.status)).length

  // Calculate revenue metrics
  const paidInvoices = invoices.filter(inv => inv.status === 'paid')
  const totalRevenue = paidInvoices.reduce((sum, inv) => sum + inv.total_amount, 0)

  const thisMonthInvoices = paidInvoices.filter(inv =>
    new Date(inv.invoice_date) >= new Date(firstDayThisMonth) &&
    new Date(inv.invoice_date) < new Date(firstDayNextMonth)
  )
  const monthlyRevenue = thisMonthInvoices.reduce((sum, inv) => sum + inv.total_amount, 0)

  const lastMonthInvoices = paidInvoices.filter(inv =>
    new Date(inv.invoice_date) >= new Date(firstDayLastMonth) &&
    new Date(inv.invoice_date) < new Date(firstDayThisMonth)
  )
  const lastMonthRevenue = lastMonthInvoices.reduce((sum, inv) => sum + inv.total_amount, 0)

  const revenueGrowth = lastMonthRevenue > 0
    ? ((monthlyRevenue - lastMonthRevenue) / lastMonthRevenue) * 100
    : 0

  // Calculate project status distribution
  const statusCounts = projects.reduce((acc, project) => {
    acc[project.status] = (acc[project.status] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const projectStatusDistribution = Object.entries(statusCounts).map(([status, count]) => ({
    status,
    count,
    percentage: totalProjects > 0 ? (count / totalProjects) * 100 : 0
  }))

  // Define quick actions
  const quickActions = [
    {
      title: "Create Invoice",
      description: "Generate a new invoice for a client",
      href: "/invoices?action=create",
      icon: "FileText"
    },
    {
      title: "Add Client",
      description: "Register a new client",
      href: "/clients?action=create",
      icon: "Users"
    },
    {
      title: "Start Project",
      description: "Begin a new project",
      href: "/projects?action=create",
      icon: "FolderOpen"
    },
    {
      title: "View Analytics",
      description: "Detailed business insights",
      href: "/analytics",
      icon: "BarChart3"
    }
  ]

  return {
    metrics: {
      totalClients,
      activeClients,
      totalProjects,
      activeProjects,
      totalInvoices,
      pendingInvoices,
      totalRevenue,
      monthlyRevenue,
      revenueGrowth
    },
    recentActivity: {
      recentInvoices,
      recentProjects,
      recentClients
    },
    projectStatusDistribution,
    quickActions
  }
}
