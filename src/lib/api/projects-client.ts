import { createClient as createSupabaseClient } from '@/lib/supabase/client'
import { CreateProjectData, UpdateProjectData } from '@/lib/types'

export async function getProjects() {
  const supabase = createSupabaseClient()

  const { data: projects, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(id, name, company),
      created_by_user:users_profiles!created_by(id, full_name)
    `)
    .order('created_at', { ascending: false })

  if (error) {
    throw new Error(`Failed to fetch projects: ${error.message}`)
  }

  // For the projects list, we don't need to fetch full team member details
  // to keep the query performant. Team member details are fetched in getProject()
  return projects.map(project => ({
    ...project,
    assigned_team_members: [] // Will be populated in individual project view
  }))
}

export async function getProject(id: string) {
  const supabase = createSupabaseClient()

  const { data: project, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(id, name, company, email, phone),
      created_by_user:users_profiles!created_by(id, full_name)
    `)
    .eq('id', id)
    .single()

  if (error) {
    throw new Error(`Failed to fetch project: ${error.message}`)
  }

  // Fetch assigned team members separately if they exist
  let assigned_team_members: Array<{ id: string; full_name: string; role: string }> = []
  if (project.assigned_team && Array.isArray(project.assigned_team)) {
    const { data: teamMembers, error: teamError } = await supabase
      .from('users_profiles')
      .select('id, full_name, role')
      .in('id', project.assigned_team)

    if (!teamError && teamMembers) {
      assigned_team_members = teamMembers
    }
  }

  return {
    ...project,
    assigned_team_members
  }
}

export async function createProject(projectData: CreateProjectData) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('projects')
    .insert(projectData)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to create project: ${error.message}`)
  }
  
  return data
}

export async function updateProject(id: string, projectData: UpdateProjectData) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('projects')
    .update(projectData)
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to update project: ${error.message}`)
  }
  
  return data
}

export async function deleteProject(id: string) {
  const supabase = createSupabaseClient()
  
  const { error } = await supabase
    .from('projects')
    .delete()
    .eq('id', id)
  
  if (error) {
    throw new Error(`Failed to delete project: ${error.message}`)
  }
}

export async function searchProjects(query: string) {
  const supabase = createSupabaseClient()

  // First, get projects that match the main table fields
  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(id, name, company),
      created_by_user:users_profiles!created_by(id, full_name),
      assigned_team_members:users_profiles(id, full_name)
    `)
    .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
    .order('created_at', { ascending: false })

  if (error) {
    throw new Error(`Failed to search projects: ${error.message}`)
  }

  // Get all projects to filter by client information client-side
  const { data: allProjects, error: allError } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(id, name, company),
      created_by_user:users_profiles!created_by(id, full_name),
      assigned_team_members:users_profiles(id, full_name)
    `)
    .order('created_at', { ascending: false })

  if (allError) {
    throw new Error(`Failed to search projects: ${allError.message}`)
  }

  // Filter client matches client-side
  const clientMatches = allProjects.filter(project =>
    (project.client?.name && project.client.name.toLowerCase().includes(query.toLowerCase())) ||
    (project.client?.company && project.client.company.toLowerCase().includes(query.toLowerCase()))
  )

  // Combine results and remove duplicates
  const combinedResults = [...data, ...clientMatches]
  const uniqueResults = combinedResults.filter((project, index, self) =>
    index === self.findIndex(p => p.id === project.id)
  )

  return uniqueResults
}

export async function getProjectsByStatus(status: string) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(id, name, company),
      created_by_user:users_profiles!created_by(id, full_name),
      assigned_team_members:users_profiles(id, full_name)
    `)
    .eq('status', status)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to fetch projects by status: ${error.message}`)
  }
  
  return data
}

export async function getProjectsByClient(clientId: string) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(id, name, company),
      created_by_user:users_profiles!created_by(id, full_name),
      assigned_team_members:users_profiles(id, full_name)
    `)
    .eq('client_id', clientId)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to fetch projects by client: ${error.message}`)
  }
  
  return data
}

export async function getAvailableClients() {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('clients')
    .select('id, name, company')
    .in('status', ['active', 'lead'])
    .order('name', { ascending: true })
  
  if (error) {
    throw new Error(`Failed to fetch available clients: ${error.message}`)
  }
  
  return data
}

export async function getAvailableUsers() {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('users_profiles')
    .select('id, full_name, role')
    .order('full_name', { ascending: true })
  
  if (error) {
    throw new Error(`Failed to fetch available users: ${error.message}`)
  }
  
  return data
}
