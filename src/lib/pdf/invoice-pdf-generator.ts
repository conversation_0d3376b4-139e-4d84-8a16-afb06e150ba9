/**
 * High-Quality Invoice PDF Generator using Puppeteer
 * 
 * This implementation provides professional PDF generation with:
 * - High-quality rendering
 * - Proper Indonesian formatting
 * - Professional typography
 * - Optimized for business use
 */

import { InvoiceWithRelations } from '@/lib/types'
import { CompanySettings, BankAccount, InvoiceTemplate } from '@/lib/types/company'

export interface PDFGenerationOptions {
  format?: 'A4' | 'Letter'
  orientation?: 'portrait' | 'landscape'
  margin?: {
    top: string
    right: string
    bottom: string
    left: string
  }
  displayHeaderFooter?: boolean
  headerTemplate?: string
  footerTemplate?: string
  printBackground?: boolean
  scale?: number
}

export interface InvoicePDFData {
  invoice: InvoiceWithRelations
  company: CompanySettings
  template?: InvoiceTemplate | null
  bankAccounts: BankAccount[]
}

export class InvoicePDFGenerator {
  private static defaultOptions: PDFGenerationOptions = {
    format: 'A4',
    orientation: 'portrait',
    margin: {
      top: '15mm',
      right: '15mm',
      bottom: '15mm',
      left: '15mm'
    },
    displayHeaderFooter: false,
    printBackground: true,
    scale: 1.0
  }

  /**
   * Generate PDF buffer from invoice data
   */
  static async generatePDF(
    data: InvoicePDFData,
    options: Partial<PDFGenerationOptions> = {}
  ): Promise<Buffer> {
    const puppeteer = await import('puppeteer')

    // Merge options with defaults
    const mergedOptions = { ...this.defaultOptions, ...options }

    console.log('Starting PDF generation with Puppeteer...')
    console.log('Environment:', {
      NODE_ENV: process.env.NODE_ENV,
      PUPPETEER_EXECUTABLE_PATH: process.env.PUPPETEER_EXECUTABLE_PATH,
      platform: process.platform
    })

    // Launch browser with optimized settings for server environment
    const browser = await puppeteer.launch({
      headless: true,
      executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-ipc-flooding-protection',
        '--font-render-hinting=none'
      ]
    })

    try {
      console.log('Browser launched successfully')
      const page = await browser.newPage()

      // Set viewport for consistent rendering
      await page.setViewport({ width: 1200, height: 800 })

      // Generate HTML content
      const html = this.generateInvoiceHTML(data)
      console.log('HTML content generated, length:', html.length)

      // Set content and wait for fonts and images to load
      console.log('Setting page content...')
      await page.setContent(html, {
        waitUntil: 'networkidle0',
        timeout: 30000
      })
      console.log('Page content set successfully')

      // Wait for images to load if there are any
      await page.evaluate(() => {
        const images = Array.from(document.images)
        return Promise.all(images.map(img => {
          if (img.complete) return Promise.resolve()
          return new Promise((resolve, reject) => {
            img.addEventListener('load', resolve)
            img.addEventListener('error', reject)
            // Set a timeout for image loading
            setTimeout(reject, 10000)
          })
        }))
      }).catch(() => {
        // Continue even if some images fail to load
        console.warn('Some images failed to load in PDF generation')
      })

      // Generate PDF with proper options
      console.log('Generating PDF...')
      const pdfBuffer = await page.pdf({
        format: mergedOptions.format,
        landscape: mergedOptions.orientation === 'landscape',
        margin: mergedOptions.margin,
        displayHeaderFooter: mergedOptions.displayHeaderFooter,
        headerTemplate: mergedOptions.headerTemplate,
        footerTemplate: mergedOptions.footerTemplate,
        printBackground: mergedOptions.printBackground,
        scale: mergedOptions.scale,
        preferCSSPageSize: true,
        timeout: 30000
      })

      console.log('PDF generated successfully, size:', pdfBuffer.length, 'bytes')
      return Buffer.from(pdfBuffer)
    } catch (error) {
      console.error('PDF generation error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : undefined
      })
      throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      console.log('Closing browser...')
      await browser.close()
      console.log('Browser closed')
    }
  }

  /**
   * Generate HTML content for PDF rendering
   */
  static generateInvoiceHTML(data: InvoicePDFData): string {
    const { invoice, company, template, bankAccounts } = data
    
    const primaryBankAccount = bankAccounts.find(account => 
      account.is_primary && account.currency === invoice.currency
    ) || bankAccounts.find(account => account.currency === invoice.currency) || bankAccounts[0]

    const showIndonesian = template?.indonesian_settings?.language === 'id' || template?.indonesian_settings?.language === 'both'
    const showEnglish = template?.indonesian_settings?.language === 'en' || template?.indonesian_settings?.language === 'both'

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice ${invoice.invoice_number}</title>
    <style>
        ${this.getInvoiceCSS()}
    </style>
</head>
<body>
    <div class="invoice-container">
        ${this.generateHeaderHTML(invoice, company, template, showIndonesian, showEnglish)}
        ${this.generateSeparatorHTML()}
        ${this.generateDetailsHTML(invoice, company, showIndonesian, showEnglish)}
        ${this.generateItemsHTML(invoice, showIndonesian, showEnglish)}
        ${this.generateTotalsHTML(invoice, showIndonesian)}
        ${this.generateNotesHTML(invoice, showIndonesian)}
        ${this.generateFooterHTML(company, primaryBankAccount, template, showIndonesian)}
    </div>
</body>
</html>`
  }

  /**
   * Generate CSS styles for PDF
   */
  private static getInvoiceCSS(): string {
    return `
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #1f2937;
            background: white;
        }

        .invoice-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
        }

        .company-info h1 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #111827;
            margin-bottom: 0.25rem;
        }

        .invoice-title {
            text-align: right;
        }

        .invoice-title h2 {
            font-size: 1.875rem;
            font-weight: 800;
            color: #111827;
            margin-bottom: 0.5rem;
        }

        .details-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0.75rem;
        }

        .items-table th {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            padding: 0.5rem;
            text-align: left;
            font-weight: 600;
            color: #111827;
            font-size: 0.85rem;
        }

        .items-table td {
            border: 1px solid #e5e7eb;
            padding: 0.5rem;
            color: #374151;
            font-size: 0.85rem;
        }

        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 1rem;
        }

        .totals-table {
            width: 280px;
        }

        .footer {
            margin-top: 1rem;
            padding-top: 0.75rem;
            border-top: 1px solid #e5e7eb;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            flex-shrink: 0;
        }

        .footer h4 {
            font-weight: 600;
            color: #111827;
            margin-bottom: 0.5rem;
            font-size: 11px;
        }

        .footer p {
            margin-bottom: 0.2rem;
            color: #374151;
            font-size: 10px;
        }

        @media print {
            body { margin: 0; }
            .invoice-container {
                max-width: none;
                height: 100vh;
                page-break-inside: avoid;
            }
        }
    `
  }

  private static generateHeaderHTML(
    invoice: InvoiceWithRelations,
    company: CompanySettings,
    template: InvoiceTemplate | null | undefined,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    const formatDate = (dateString: string) => {
      const date = new Date(dateString)
      if (template?.indonesian_settings?.date_format === 'dd/mm/yyyy') {
        return date.toLocaleDateString('id-ID', { day: '2-digit', month: '2-digit', year: 'numeric' }).replace(/\//g, '/')
      } else if (template?.indonesian_settings?.date_format === 'dd-mm-yyyy') {
        return date.toLocaleDateString('id-ID', { day: '2-digit', month: '2-digit', year: 'numeric' }).replace(/\//g, '-')
      }
      return date.toLocaleDateString('id-ID', { day: '2-digit', month: 'short', year: 'numeric' })
    }

    return `
        <!-- Enhanced Modern Header with Company Information -->
        <div class="invoice-header" style="margin-bottom: 1rem;">
            <div style="display: flex; align-items: flex-start; justify-content: space-between;">
                <!-- Company Logo and Information -->
                <div style="display: flex; flex-direction: column;">
                    ${template?.layout?.show_logo && company.logo_url ? `
                        <div class="company-logo" style="margin-bottom: 0.5rem;">
                            <img src="${company.logo_url}" alt="${company.name}" style="height: 50px; width: auto; object-fit: contain;" />
                        </div>
                    ` : ''}

                    <!-- Company Information -->
                    <div style="line-height: 1.4;">
                        <p style="font-weight: 600; color: #111827; font-size: 1rem; margin-bottom: 0.5rem;">${company.name}</p>
                        <div style="font-size: 0.8rem; color: #6b7280; line-height: 1.3;">
                            ${company.address ? `
                                <div style="margin-bottom: 0.5rem;">
                                    <p style="margin-bottom: 0.2rem;">${company.address.street}</p>
                                    <p style="margin-bottom: 0.2rem;">${company.address.city}, ${company.address.state} ${company.address.postal_code}</p>
                                    <p style="margin-bottom: 0.2rem;">${company.address.country}</p>
                                </div>
                            ` : ''}
                            <div style="padding-top: 0.5rem;">
                                <p style="margin-bottom: 0.2rem;">${company.email}</p>
                                <p style="margin-bottom: 0.2rem;">${company.phone}</p>
                                ${company.website ? `<p style="margin-bottom: 0.2rem;">${company.website}</p>` : ''}
                                ${template?.content?.show_tax_id && company.tax_id ? `
                                    <p style="font-size: 0.7rem; color: #9ca3af;">NPWP: ${company.tax_id}</p>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Title, Number and Details - Enhanced Style -->
                <div style="text-align: right;">
                    <div style="font-size: 0.8rem; font-weight: 500; color: #6b7280; margin-bottom: 0.5rem; text-transform: uppercase; letter-spacing: 0.05em;">
                        ${showIndonesian && showEnglish ? 'Invoice / Faktur' : showIndonesian ? 'Faktur' : 'Invoice'}
                    </div>
                    <div style="font-size: 1.5rem; font-weight: bold; color: #111827; margin-bottom: 0.75rem;">
                        #${invoice.invoice_number}
                    </div>
                    ${invoice.milestone_type && invoice.milestone_type !== 'standard' ? `
                        <div style="display: inline-flex; align-items: center; padding: 0.3rem 0.8rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500; background: #eff6ff; color: #1d4ed8; border: 1px solid #dbeafe; margin-bottom: 1rem;">
                            ${invoice.milestone_type === 'dp' ? (showIndonesian ? 'Uang Muka' : 'Down Payment') : ''}
                            ${invoice.milestone_type === 'progress' ? (showIndonesian ? 'Pembayaran Bertahap' : 'Progress Payment') : ''}
                            ${invoice.milestone_type === 'final' ? (showIndonesian ? 'Pembayaran Akhir' : 'Final Payment') : ''}
                            ${invoice.milestone_percentage ? ` (${invoice.milestone_percentage}%)` : ''}
                        </div>
                    ` : ''}

                    <!-- Invoice Details - Moved to Header -->
                    <div style="margin-top: 1rem;">
                        <div style="font-size: 0.7rem; font-weight: 600; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: 0.75rem;">
                            ${showIndonesian && showEnglish ? 'Invoice Details / Detail Faktur' : showIndonesian ? 'Detail Faktur' : 'Invoice Details'}
                        </div>
                        <div style="font-size: 0.8rem; line-height: 1.4;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                <span style="color: #6b7280;">
                                    ${showIndonesian ? 'Tanggal Faktur' : 'Invoice Date'}
                                </span>
                                <span style="font-weight: 500; color: #111827;">${formatDate(invoice.created_at)}</span>
                            </div>
                            ${invoice.due_date ? `
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                    <span style="color: #6b7280;">
                                        ${showIndonesian ? 'Jatuh Tempo' : 'Due Date'}
                                    </span>
                                    <span style="font-weight: 500; color: #111827;">${formatDate(invoice.due_date)}</span>
                                </div>
                            ` : ''}
                            ${invoice.project ? `
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="color: #6b7280;">
                                        ${showIndonesian ? 'Proyek' : 'Project'}
                                    </span>
                                    <span style="font-weight: 500; color: #111827;">${invoice.project.name}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
  }

  /**
   * Generate enhanced separator line
   */
  private static generateSeparatorHTML(): string {
    return `
        <div style="margin: 1.5rem 0; height: 1px; background: linear-gradient(to right, #e5e7eb, #d1d5db, #e5e7eb);"></div>
    `
  }

  private static generateDetailsHTML(
    invoice: InvoiceWithRelations,
    company: CompanySettings,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    return `
        <!-- Enhanced Bill To Section - Wider Layout -->
        <div style="margin-bottom: 1rem;">
            <div style="background: #f9fafb; border-radius: 0.5rem; padding: 1rem; max-width: 80%;">
                <div style="font-size: 0.75rem; font-weight: 600; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: 0.75rem;">
                    ${showIndonesian && showEnglish ? 'Bill To / Tagihan Kepada' : showIndonesian ? 'Tagihan Kepada' : 'Bill To'}
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div style="line-height: 1.4;">
                        <p style="font-weight: 500; color: #111827; font-size: 1rem; margin-bottom: 0.5rem;">${invoice.client?.name || ''}</p>
                        ${invoice.client?.company ? `<p style="color: #6b7280; font-weight: 500; margin-bottom: 0.75rem;">${invoice.client.company}</p>` : ''}
                        ${invoice.client?.address && typeof invoice.client.address === 'object' ? `
                            <div style="color: #6b7280; font-size: 0.85rem; line-height: 1.3; margin-bottom: 0.75rem;">
                                ${invoice.client.address.street ? `<p style="margin-bottom: 0.2rem;">${invoice.client.address.street}</p>` : ''}
                                ${(invoice.client.address.city || invoice.client.address.state) ? `
                                    <p style="margin-bottom: 0.2rem;">${[invoice.client.address.city, invoice.client.address.state].filter(Boolean).join(', ')}</p>
                                ` : ''}
                                ${invoice.client.address.postal_code ? `<p>${invoice.client.address.postal_code}</p>` : ''}
                            </div>
                        ` : ''}
                    </div>
                    <div style="line-height: 1.4;">
                        ${invoice.client?.email ? `<p style="color: #6b7280; font-size: 0.85rem; margin-bottom: 0.3rem;">${invoice.client.email}</p>` : ''}
                        ${invoice.client?.phone ? `<p style="color: #6b7280; font-size: 0.85rem;">${invoice.client.phone}</p>` : ''}
                    </div>
                </div>
            </div>
        </div>
    `
  }

  private static generateItemsHTML(
    invoice: InvoiceWithRelations,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    const items = Array.isArray(invoice.items) ? invoice.items : []

    return `
        <table class="items-table" style="margin-bottom: 1rem;">
            <thead>
                <tr>
                    <th style="width: 50%; font-size: 0.85rem; padding: 0.5rem;">${showIndonesian && showEnglish ? 'Description / Deskripsi' : showIndonesian ? 'Deskripsi' : 'Description'}</th>
                    <th style="width: 16.67%; text-align: center; font-size: 0.85rem; padding: 0.5rem;">${showIndonesian ? 'Jumlah' : 'Qty'}</th>
                    <th style="width: 16.67%; text-align: center; font-size: 0.85rem; padding: 0.5rem;">${showIndonesian ? 'Harga' : 'Rate'}</th>
                    <th style="width: 16.67%; text-align: right; font-size: 0.85rem; padding: 0.5rem;">${showIndonesian ? 'Total' : 'Amount'}</th>
                </tr>
            </thead>
            <tbody>
                ${(items as { description?: string; quantity?: number; rate?: number; amount?: number }[]).map((item) => `
                    <tr>
                        <td style="font-size: 0.85rem; padding: 0.5rem;">${item.description || ''}</td>
                        <td style="text-align: center; font-size: 0.85rem; padding: 0.5rem;">${item.quantity || 0}</td>
                        <td style="text-align: center; font-size: 0.85rem; padding: 0.5rem;">${this.formatCurrency(item.rate || 0, invoice.currency)}</td>
                        <td style="text-align: right; font-weight: 600; font-size: 0.85rem; padding: 0.5rem;">${this.formatCurrency(item.amount || 0, invoice.currency)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `
  }

  private static generateTotalsHTML(
    invoice: InvoiceWithRelations,
    showIndonesian: boolean
  ): string {
    return `
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td style="padding: 0.5rem 0; color: #6b7280; font-size: 0.9rem;">${showIndonesian ? 'Subtotal:' : 'Subtotal:'}</td>
                    <td style="padding: 0.5rem 0; text-align: right; font-weight: 600; font-size: 0.9rem;">${this.formatCurrency(invoice.amount, invoice.currency)}</td>
                </tr>
                ${invoice.discount_amount && invoice.discount_amount > 0 ? `
                    <tr>
                        <td style="padding: 0.5rem 0; color: #6b7280; font-size: 0.9rem;">
                            ${showIndonesian ? 'Diskon:' : 'Discount:'}
                            ${invoice.discount_type === 'percentage' ? ` (${invoice.discount_amount}%)` : ''}
                        </td>
                        <td style="padding: 0.5rem 0; text-align: right; font-weight: 600; color: #dc2626; font-size: 0.9rem;">
                            -${this.formatCurrency(
                              invoice.discount_type === 'percentage'
                                ? (invoice.amount * invoice.discount_amount / 100)
                                : invoice.discount_amount,
                              invoice.currency
                            )}
                        </td>
                    </tr>
                ` : ''}
                ${invoice.tax_amount > 0 ? `
                    <tr>
                        <td style="padding: 0.5rem 0; color: #6b7280; font-size: 0.9rem;">${showIndonesian ? 'Pajak:' : 'Tax:'}</td>
                        <td style="padding: 0.5rem 0; text-align: right; font-weight: 600; font-size: 0.9rem;">${this.formatCurrency(invoice.tax_amount, invoice.currency)}</td>
                    </tr>
                ` : ''}
                <tr style="border-top: 2px solid #374151; border-bottom: 3px double #374151;">
                    <td style="padding: 0.75rem 0; font-size: 1.125rem; font-weight: 700; color: #111827;">${showIndonesian ? 'Total:' : 'Total:'}</td>
                    <td style="padding: 0.75rem 0; text-align: right; font-size: 1.125rem; font-weight: 700; color: #111827;">${this.formatCurrency(invoice.total_amount, invoice.currency)}</td>
                </tr>
            </table>
        </div>
    `
  }

  private static generateNotesHTML(
    invoice: InvoiceWithRelations,
    showIndonesian: boolean
  ): string {
    if (!invoice.notes) return ''
    
    return `
        <div style="margin-bottom: 2rem;">
            <h3 style="font-size: 1.125rem; font-weight: 600; color: #111827; margin-bottom: 0.5rem; border-bottom: 1px solid #e5e7eb; padding-bottom: 0.25rem;">
                ${showIndonesian ? 'Catatan' : 'Notes'}
            </h3>
            <p style="color: #374151; line-height: 1.6;">${invoice.notes}</p>
        </div>
    `
  }

  private static generateFooterHTML(
    company: CompanySettings,
    primaryBankAccount: { bank_name?: string; account_number?: string; account_name?: string } | null,
    template: InvoiceTemplate | null | undefined,
    showIndonesian: boolean
  ): string {
    return `
        <!-- Enhanced Payment Information Footer -->
        <div style="margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid #e5e7eb;">
            <!-- Essential Payment Information Only -->
            ${template?.content?.show_bank_details && primaryBankAccount ? `
                <div style="background: #f9fafb; border-radius: 0.5rem; padding: 1rem; margin-bottom: 1rem;">
                    <div style="text-align: center;">
                        <div style="font-size: 0.75rem; font-weight: 600; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: 0.75rem;">
                            ${showIndonesian ? 'Informasi Pembayaran' : 'Payment Information'}
                        </div>
                        <div style="line-height: 1.4;">
                            <p style="font-weight: 600; color: #111827; font-size: 1.2rem; margin-bottom: 0.75rem;">${primaryBankAccount.bank_name}</p>
                            <div style="display: flex; justify-content: center; align-items: center; gap: 2rem; font-size: 0.9rem;">
                                <div style="text-align: center;">
                                    <p style="color: #6b7280; font-size: 0.75rem; text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: 0.3rem;">
                                        ${showIndonesian ? 'Nama Rekening' : 'Account Name'}
                                    </p>
                                    <p style="font-weight: 500; color: #111827; font-size: 0.9rem;">${primaryBankAccount.account_name}</p>
                                </div>
                                <div style="text-align: center;">
                                    <p style="color: #6b7280; font-size: 0.75rem; text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: 0.3rem;">
                                        ${showIndonesian ? 'Nomor Rekening' : 'Account Number'}
                                    </p>
                                    <p style="font-family: monospace; font-weight: bold; color: #111827; font-size: 1.1rem;">${primaryBankAccount.account_number}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            ` : ''}

            <!-- Footer Text -->
            ${company.invoice_settings?.invoice_footer_text ? `
                <div style="text-align: center; margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid #f3f4f6;">
                    <p style="color: #9ca3af; font-size: 0.8rem; line-height: 1.4;">
                        ${company.invoice_settings.invoice_footer_text}
                    </p>
                </div>
            ` : ''}
        </div>
    `
  }

  private static formatCurrency(amount: number, currency: string): string {
    if (currency === 'IDR') {
      return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount)
    }
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }
}
